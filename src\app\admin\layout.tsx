'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import Link from 'next/link';
import { signOut } from 'next-auth/react';
import {
  FileText,
  Users,
  BarChart3,
  Settings,
  LogOut,
  Home,
  UserPlus,
  Search
} from 'lucide-react';
import MobileNavigation from '@/components/navigation/MobileNavigation';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();

  useEffect(() => {
    console.log('🔐 Admin layout auth check:', { status, session: session?.user });

    if (status === 'loading') return; // Still loading

    if (!session) {
      console.log('❌ No session in admin layout, redirecting to signin');
      window.location.href = '/auth/signin?callbackUrl=/admin';
      return;
    }

    if (session.user?.role !== 'admin') {
      console.log('❌ Non-admin user in admin layout, redirecting to dashboard');
      window.location.href = '/dashboard'; // Redirect non-admin users
      return;
    }

    console.log('✅ Admin access granted');
  }, [session, status]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || session.user?.role !== 'admin') {
    return null;
  }

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: Home },
    { name: 'Candidates', href: '/admin/candidates', icon: Users },
    { name: 'Add Candidate', href: '/admin/candidates/new', icon: UserPlus },
    { name: 'Test Results', href: '/admin/results', icon: BarChart3 },
    { name: 'Advanced Search', href: '/admin/search', icon: Search },
    { name: 'Reports', href: '/admin/reports', icon: FileText },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Navigation */}
      <MobileNavigation
        navigation={navigation}
        userInfo={{
          name: session.user?.name,
          email: session.user?.email,
          role: 'admin'
        }}
        title="IELTS Admin"
        subtitle="Certification System"
      />

      {/* Desktop Sidebar */}
      <div className="mobile-nav-hidden fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center px-6 py-4 border-b border-gray-200">
            <FileText className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-lg font-semibold text-gray-900">IELTS Admin</h1>
              <p className="text-sm text-gray-500">Certification System</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 group"
                >
                  <Icon className="h-5 w-5 mr-3 text-gray-400 group-hover:text-gray-500" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info and logout */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center mb-3">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{session.user?.name}</p>
                <p className="text-xs text-gray-500">{session.user?.email}</p>
                <p className="text-xs text-blue-600 font-medium">Administrator</p>
              </div>
            </div>
            <button
              onClick={() => signOut({ callbackUrl: '/' })}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900"
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign out
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        <main className="py-4 lg:py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
