'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  Brain,
  Search,
  Sparkles,
  FileText,
  TrendingUp,
  Target,
  BookOpen,
  ArrowLeft,
  Save,
  RefreshCw
} from 'lucide-react';

interface TestResult {
  id: string;
  candidateId: string;
  listeningBandScore: number | null;
  readingBandScore: number | null;
  writingBandScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  candidate: {
    fullName: string;
    passportNumber: string;
  };
}

interface AIFeedback {
  overallAssessment: string;
  strengths: string[];
  areasForImprovement: string[];
  specificRecommendations: {
    listening: string;
    reading: string;
    writing: string;
    speaking: string;
  };
  studyPlan: string;
  nextSteps: string[];
}

export default function FeedbackPage() {
  const searchParams = useSearchParams();
  const resultId = searchParams.get('resultId');

  const [selectedResultId, setSelectedResultId] = useState(resultId || '');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<TestResult[]>([]);

  useEffect(() => {
    if (selectedResultId) {
      fetchTestResult(selectedResultId);
    }
  }, [selectedResultId]);

  const fetchTestResult = async (id: string) => {
    try {
      const response = await fetch(`/api/checker/results/${id}`);
      if (response.ok) {
        const data = await response.json();
        setTestResult(data);
      }
    } catch (error) {
      console.error('Error fetching test result:', error);
    }
  };

  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      const response = await fetch('/api/checker/results/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: searchQuery }),
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      }
    } catch (error) {
      console.error('Error searching results:', error);
    }
  };

  const generateFeedback = async () => {
    if (!testResult) return;

    setIsGenerating(true);
    setError('');

    try {
      const response = await fetch('/api/ai/generate-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resultId: selectedResultId,
          scores: {
            listening: testResult.listeningBandScore,
            reading: testResult.readingBandScore,
            writing: testResult.writingBandScore,
            speaking: testResult.speakingBandScore,
            overall: testResult.overallBandScore,
          },
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setFeedback(data.feedback);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to generate feedback');
      }
    } catch (err) {
      console.error('Error generating feedback:', err);
      setError('An error occurred while generating feedback');
    } finally {
      setIsGenerating(false);
    }
  };

  const saveFeedback = async () => {
    if (!feedback || !selectedResultId) return;

    setIsSaving(true);

    try {
      const response = await fetch('/api/ai/save-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resultId: selectedResultId,
          feedback,
        }),
      });

      if (response.ok) {
        // Show success message or redirect
        alert('Feedback saved successfully!');
      } else {
        setError('Failed to save feedback');
      }
    } catch (err) {
      console.error('Error saving feedback:', err);
      setError('An error occurred while saving feedback');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="mobile-space-y">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mobile-space-y sm:space-y-0">
        <div className="flex items-center">
          <Link
            href="/dashboard"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600 touch-target"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="mobile-title text-gray-900">AI Feedback Generator</h1>
            <p className="mobile-body text-gray-600 mt-1">Generate personalized feedback for IELTS test results</p>
          </div>
        </div>
      </div>

      {/* Result Selection */}
      {!selectedResultId && (
        <div className="bg-white shadow rounded-lg mobile-card">
          <h3 className="mobile-subtitle text-gray-900 mb-4 flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Select Test Result
          </h3>

          <div className="flex flex-col sm:flex-row mobile-gap mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by candidate name or passport number..."
              className="mobile-input flex-1"
            />
            <button
              onClick={performSearch}
              className="mobile-button-primary w-full sm:w-auto"
            >
              Search
            </button>
          </div>

          {searchResults.length > 0 && (
            <div className="space-y-2">
              {searchResults.map((result) => (
                <div
                  key={result.id}
                  onClick={() => setSelectedResultId(result.id)}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">{result.candidate.fullName}</h4>
                      <p className="text-sm text-gray-500">{result.candidate.passportNumber}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        Overall: {result.overallBandScore || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-500">
                        L:{result.listeningBandScore} R:{result.readingBandScore} W:{result.writingBandScore} S:{result.speakingBandScore}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Selected Result Info */}
      {testResult && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-900">{testResult.candidate.fullName}</h3>
              <p className="text-sm text-blue-700">
                {testResult.candidate.passportNumber} • Overall Band: {testResult.overallBandScore || 'N/A'}
              </p>
            </div>
            <button
              onClick={() => {
                setSelectedResultId('');
                setTestResult(null);
                setFeedback(null);
              }}
              className="text-blue-600 hover:text-blue-800"
            >
              Change Result
            </button>
          </div>
        </div>
      )}

      {/* Generate Feedback */}
      {testResult && !feedback && (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <Brain className="h-16 w-16 text-purple-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Generate AI Feedback</h3>
          <p className="text-gray-600 mb-6">
            Create personalized feedback and recommendations based on the test scores
          </p>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
              {error}
            </div>
          )}

          <button
            onClick={generateFeedback}
            disabled={isGenerating}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="animate-spin h-5 w-5 mr-2" />
                Generating Feedback...
              </>
            ) : (
              <>
                <Sparkles className="h-5 w-5 mr-2" />
                Generate AI Feedback
              </>
            )}
          </button>
        </div>
      )}

      {/* Generated Feedback */}
      {feedback && (
        <div className="space-y-6">
          {/* Overall Assessment */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2 text-blue-600" />
              Overall Assessment
            </h3>
            <p className="text-gray-700 leading-relaxed">{feedback.overallAssessment}</p>
          </div>

          {/* Strengths and Areas for Improvement */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                Strengths
              </h3>
              <ul className="space-y-2">
                {feedback.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 h-2 w-2 bg-green-400 rounded-full mt-2 mr-3"></span>
                    <span className="text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Target className="h-5 w-5 mr-2 text-orange-600" />
                Areas for Improvement
              </h3>
              <ul className="space-y-2">
                {feedback.areasForImprovement.map((area, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 h-2 w-2 bg-orange-400 rounded-full mt-2 mr-3"></span>
                    <span className="text-gray-700">{area}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Specific Recommendations */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-purple-600" />
              Specific Recommendations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Listening</h4>
                <p className="text-sm text-gray-700 mb-4">{feedback.specificRecommendations.listening}</p>

                <h4 className="font-medium text-green-900 mb-2">Reading</h4>
                <p className="text-sm text-gray-700">{feedback.specificRecommendations.reading}</p>
              </div>
              <div>
                <h4 className="font-medium text-purple-900 mb-2">Writing</h4>
                <p className="text-sm text-gray-700 mb-4">{feedback.specificRecommendations.writing}</p>

                <h4 className="font-medium text-red-900 mb-2">Speaking</h4>
                <p className="text-sm text-gray-700">{feedback.specificRecommendations.speaking}</p>
              </div>
            </div>
          </div>

          {/* Study Plan */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recommended Study Plan</h3>
            <p className="text-gray-700 leading-relaxed mb-4">{feedback.studyPlan}</p>

            <h4 className="font-medium text-gray-900 mb-2">Next Steps</h4>
            <ul className="space-y-2">
              {feedback.nextSteps.map((step, index) => (
                <li key={index} className="flex items-start">
                  <span className="flex-shrink-0 h-6 w-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{step}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={generateFeedback}
              disabled={isGenerating}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2 inline" />
              Regenerate
            </button>
            <button
              onClick={saveFeedback}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Feedback
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
