'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Search,
  User,
  Calendar,
  MapPin,
  FileText,
  Plus,
  Eye,
  ClipboardList
} from 'lucide-react';

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  nationality: string;
  passportNumber: string;
  testDate: string;
  testCenter: string;
  photoUrl?: string;
  hasResults?: boolean;
  resultId?: string;
}

export default function CandidateSearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<'passport' | 'birth_certificate'>('passport'); // Restricted to only these options
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setHasSearched(true);

    try {
      const response = await fetch('/api/checker/candidates/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          searchType,
        }),
      });

      if (response.ok) {
        const results = await response.json();
        setCandidates(results);
      } else {
        setCandidates([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setCandidates([]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mobile-space-y">
      {/* Header */}
      <div className="mobile-card">
        <h1 className="mobile-title text-gray-900">Search Candidates</h1>
        <p className="mobile-body text-gray-600 mt-2">Find candidates to enter or view test results</p>
      </div>

      {/* Search Form */}
      <div className="bg-white shadow rounded-lg mobile-card">
        <form onSubmit={handleSearch} className="mobile-space-y">
          <div className="mobile-grid-1 lg:grid-cols-3 mobile-gap">
            <div>
              <label htmlFor="searchType" className="block mobile-caption font-medium text-gray-700 mb-2">
                Search by
              </label>
              <select
                id="searchType"
                value={searchType}
                onChange={(e) => setSearchType(e.target.value as 'passport' | 'birth_certificate')}
                className="mobile-select"
              >
                <option value="passport">Passport Number</option>
                <option value="birth_certificate">Birth Certificate Number</option>
              </select>
            </div>

            <div className="lg:col-span-2">
              <label htmlFor="searchQuery" className="block mobile-caption font-medium text-gray-700 mb-2">
                Search term
              </label>
              <div className="flex flex-col sm:flex-row mobile-gap">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    id="searchQuery"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={
                      searchType === 'passport' ? 'Enter passport number...' :
                      'Enter birth certificate number...'
                    }
                    className="mobile-input pl-10 sm:rounded-l-md sm:rounded-r-none"
                    required
                  />
                </div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="mobile-button-primary sm:rounded-l-none sm:rounded-r-md disabled:opacity-50"
                >
                  {isLoading ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Search Results */}
      {hasSearched && (
        <div className="bg-white shadow rounded-lg">
          <div className="mobile-card border-b border-gray-200">
            <h3 className="mobile-subtitle text-gray-900">
              Search Results {candidates.length > 0 && `(${candidates.length})`}
            </h3>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 mobile-body text-gray-600">Searching candidates...</span>
            </div>
          ) : candidates.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {candidates.map((candidate) => (
                <div key={candidate.id} className="mobile-card hover:bg-gray-50">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mobile-space-y lg:space-y-0">
                    <div className="flex items-center mobile-space-x">
                      {candidate.photoUrl ? (
                        <Image
                          className="h-12 w-12 sm:h-16 sm:w-16 rounded-full object-cover flex-shrink-0"
                          src={candidate.photoUrl}
                          alt={candidate.fullName}
                          width={64}
                          height={64}
                        />
                      ) : (
                        <div className="h-12 w-12 sm:h-16 sm:w-16 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                          <User className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400" />
                        </div>
                      )}

                      <div className="flex-1 min-w-0">
                        <h4 className="mobile-subtitle font-medium text-gray-900 truncate">{candidate.fullName}</h4>
                        <div className="mt-1 mobile-space-y">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 mobile-caption text-gray-500">
                            <span className="flex items-center mb-1 sm:mb-0">
                              <FileText className="h-4 w-4 mr-1 flex-shrink-0" />
                              <span className="truncate">{candidate.passportNumber}</span>
                            </span>
                            <span className="flex items-center mb-1 sm:mb-0">
                              <Calendar className="h-4 w-4 mr-1 flex-shrink-0" />
                              <span className="truncate">Test: {new Date(candidate.testDate).toLocaleDateString()}</span>
                            </span>
                            <span className="flex items-center">
                              <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                              <span className="truncate">{candidate.testCenter}</span>
                            </span>
                          </div>
                          <div className="mobile-caption text-gray-600 truncate">
                            {candidate.email} • {candidate.phoneNumber}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center mobile-space-y sm:space-y-0 sm:space-x-3 mt-4 lg:mt-0">
                      {candidate.hasResults ? (
                        <>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full mobile-caption font-medium bg-green-100 text-green-800 mb-2 sm:mb-0">
                            Results Available
                          </span>
                          <Link
                            href={`/dashboard/results/${candidate.resultId}`}
                            className="mobile-button mobile-button-secondary w-full sm:w-auto text-center"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Results
                          </Link>
                        </>
                      ) : (
                        <>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full mobile-caption font-medium bg-yellow-100 text-yellow-800 mb-2 sm:mb-0">
                            No Results
                          </span>
                          <Link
                            href={`/dashboard/results/new?candidateId=${candidate.id}`}
                            className="mobile-button-primary w-full sm:w-auto text-center"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Enter Results
                          </Link>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center mobile-card">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="mobile-subtitle font-medium text-gray-900 mb-2">No candidates found</h3>
              <p className="mobile-body text-gray-600">
                Try adjusting your search criteria or check the spelling.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      {!hasSearched && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg mobile-card">
          <h3 className="mobile-subtitle text-blue-900 mb-4">Quick Actions</h3>
          <div className="mobile-grid-2 mobile-gap">
            <Link
              href="/dashboard/results/list"
              className="flex items-center mobile-card-compact bg-white rounded-lg shadow hover:shadow-md transition-shadow touch-target"
            >
              <ClipboardList className="h-8 w-8 text-blue-600 mr-4 flex-shrink-0" />
              <div className="min-w-0">
                <h4 className="mobile-body font-medium text-gray-900 truncate">View All Results</h4>
                <p className="mobile-caption text-gray-600 truncate">Browse all entered test results</p>
              </div>
            </Link>
            <Link
              href="/dashboard/results"
              className="flex items-center mobile-card-compact bg-white rounded-lg shadow hover:shadow-md transition-shadow touch-target"
            >
              <Plus className="h-8 w-8 text-green-600 mr-4 flex-shrink-0" />
              <div className="min-w-0">
                <h4 className="mobile-body font-medium text-gray-900 truncate">Enter New Results</h4>
                <p className="mobile-caption text-gray-600 truncate">Add test results for a candidate</p>
              </div>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
